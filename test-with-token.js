const http = require('http');

// Replace with your token
const API_TOKEN = process.argv[2] || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.GlYaOtfrD4y62wzVOcW5BMgiOvLJT-QikMwOWAn5vsE';

if (!API_TOKEN) {
  console.error('Please provide an API token as a command line argument:');
  console.error('node test-with-token.js YOUR_API_TOKEN');
  process.exit(1);
}

// Test various endpoints
const endpoints = [
  { path: '/user', description: 'User endpoint' },
  { path: '/team', description: 'Team endpoint' },
  { path: '/project', description: 'Project endpoint' }
];

function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 4019,
      path: endpoint.path,
      method: 'GET',
      timeout: 5000,
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    console.log(`Testing ${endpoint.description} (${endpoint.path}) with token...`);

    const req = http.request(options, (res) => {
      console.log(`${endpoint.description} - Status Code: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (data) {
          try {
            const jsonData = JSON.parse(data);
            console.log(`${endpoint.description} - Response: ${JSON.stringify(jsonData).substring(0, 100)}${JSON.stringify(jsonData).length > 100 ? '...' : ''}`);
          } catch (e) {
            console.log(`${endpoint.description} - Response: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
          }
        }
        resolve();
      });
    });

    req.on('error', (error) => {
      console.error(`${endpoint.description} - Error: ${error.message}`);
      resolve();
    });

    req.on('timeout', () => {
      console.error(`${endpoint.description} - Request timed out`);
      req.destroy();
      resolve();
    });

    req.end();
  });
}

async function testAllEndpoints() {
  console.log(`Testing Chartbrew API endpoints with token: ${API_TOKEN.substring(0, 10)}...\n`);
  
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
    console.log(''); // Add a blank line between tests
  }
  
  console.log('\nAll tests completed!');
}

testAllEndpoints();
