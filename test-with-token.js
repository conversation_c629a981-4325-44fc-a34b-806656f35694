const http = require('http');

// Strapi 5 API Token Test
// Usage: node test-with-token.js [YOUR_STRAPI_API_TOKEN]

const API_TOKEN = process.argv[2];

if (!API_TOKEN) {
  console.error('🔑 Please provide a Strapi API token as a command line argument:');
  console.error('   node test-with-token.js YOUR_STRAPI_API_TOKEN');
  console.error('');
  console.error('📋 To generate a Strapi API token:');
  console.error('   1. Go to http://localhost:1337/admin');
  console.error('   2. Login to Strapi admin');
  console.error('   3. Go to Settings → API Tokens');
  console.error('   4. Click "Create new API Token"');
  console.error('   5. Set name, description, and permissions');
  console.error('   6. Copy the generated token');
  process.exit(1);
}

// Test various Strapi 5 API endpoints that actually exist in your system
const endpoints = [
  { path: '/api/properties', description: 'Properties endpoint' },
  { path: '/api/properties/featured', description: 'Featured properties endpoint' },
  { path: '/api/projects', description: 'Projects endpoint' },
  { path: '/api/nearby-place-categories', description: 'Nearby place categories endpoint' },
  { path: '/api/nearby-place-categories/enabled', description: 'Enabled categories endpoint' },
  { path: '/api/upload/files', description: 'Upload files endpoint' },
  { path: '/api/memberships', description: 'Memberships endpoint' },
  { path: '/api/messages', description: 'Messages endpoint' }
];

function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 1337,  // Strapi default port
      path: endpoint.path,
      method: 'GET',
      timeout: 10000,
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    console.log(`\n📡 Testing ${endpoint.description}`);
    console.log(`   URL: http://localhost:1337${endpoint.path}`);
    console.log(`   Token: ${API_TOKEN.substring(0, 20)}...`);

    const req = http.request(options, (res) => {
      const statusColor = res.statusCode === 200 ? '\x1b[32m' : res.statusCode < 400 ? '\x1b[33m' : '\x1b[31m';
      const resetColor = '\x1b[0m';

      console.log(`   Status: ${statusColor}${res.statusCode}${resetColor}`);

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        if (data) {
          try {
            const jsonData = JSON.parse(data);

            // Handle different response structures
            if (res.statusCode === 200) {
              console.log(`   ✅ Success!`);

              // Show relevant data based on endpoint
              if (endpoint.path === '/api/properties' && jsonData.data) {
                console.log(`   📊 Found ${jsonData.data.length} properties`);
                if (jsonData.meta) {
                  console.log(`   📈 Pagination: ${JSON.stringify(jsonData.meta.pagination)}`);
                }
              } else if (endpoint.path === '/api/users-permissions/users/me') {
                console.log(`   👤 User: ${jsonData.username || jsonData.email || 'Unknown'}`);
                console.log(`   🆔 ID: ${jsonData.id}`);
              } else if (endpoint.path === '/api/upload/files' && jsonData.data) {
                console.log(`   📁 Found ${jsonData.data.length} files`);
              } else {
                const preview = JSON.stringify(jsonData).substring(0, 150);
                console.log(`   📄 Response: ${preview}${preview.length >= 150 ? '...' : ''}`);
              }
            } else {
              console.log(`   ❌ Error Response:`);
              if (jsonData.error) {
                console.log(`      Message: ${jsonData.error.message || 'Unknown error'}`);
                console.log(`      Status: ${jsonData.error.status || 'Unknown status'}`);
              } else {
                const preview = JSON.stringify(jsonData).substring(0, 100);
                console.log(`      ${preview}${preview.length >= 100 ? '...' : ''}`);
              }
            }
          } catch (e) {
            console.log(`   📄 Raw Response: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`);
          }
        } else {
          console.log(`   📄 Empty response`);
        }
        resolve({ status: res.statusCode, success: res.statusCode === 200 });
      });
    });

    req.on('error', (error) => {
      console.error(`   ❌ Connection Error: ${error.message}`);
      resolve({ status: 0, success: false, error: error.message });
    });

    req.on('timeout', () => {
      console.error(`   ⏰ Request timed out`);
      req.destroy();
      resolve({ status: 0, success: false, error: 'Timeout' });
    });

    req.end();
  });
}

async function testAllEndpoints() {
  console.log(`🚀 Testing Strapi 5 API Authentication`);
  console.log(`🕒 ${new Date().toLocaleString()}`);
  console.log(`🔑 Token: ${API_TOKEN.substring(0, 20)}...`);
  console.log(`🌐 Server: http://localhost:1337`);
  console.log('='.repeat(60));

  const results = [];

  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push({ ...endpoint, ...result });
  }

  // Summary
  console.log('\n📊 Test Summary:');
  console.log('='.repeat(60));

  const successful = results.filter(r => r.success).length;
  const total = results.length;

  console.log(`✅ Successful: ${successful}/${total}`);
  console.log(`❌ Failed: ${total - successful}/${total}`);

  if (successful === total) {
    console.log('\n🎉 All tests passed! Your Strapi API token is working correctly.');
    console.log('✨ You can use this token for API requests to your Strapi application.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the details above.');

    const failed = results.filter(r => !r.success);
    if (failed.length > 0) {
      console.log('\n🔍 Failed endpoints:');
      failed.forEach(f => {
        console.log(`   • ${f.description} (${f.path}) - Status: ${f.status}`);
      });

      console.log('\n💡 Troubleshooting tips:');
      console.log('   1. Ensure your API token has the correct permissions');
      console.log('   2. Check if the endpoints exist in your Strapi configuration');
      console.log('   3. Verify that Strapi is running on http://localhost:1337');
      console.log('   4. Check Strapi admin panel for API token settings');
    }
  }

  console.log('\n📋 Next steps:');
  console.log('   • Use this token in your frontend application');
  console.log('   • Configure API permissions in Strapi admin panel');
  console.log('   • Test specific endpoints your application needs');
}

// Additional token validation function
async function validateTokenFormat() {
  console.log('\n🔍 Validating token format...');

  if (!API_TOKEN || typeof API_TOKEN !== 'string') {
    console.log('❌ Invalid token: Token must be a non-empty string');
    return false;
  }

  if (API_TOKEN.length < 10) {
    console.log('❌ Invalid token: Token appears too short');
    return false;
  }

  // Check if it looks like a JWT (has dots) or Strapi API token
  if (API_TOKEN.includes('.')) {
    console.log('ℹ️  Token format: JWT (JSON Web Token)');
    const parts = API_TOKEN.split('.');
    if (parts.length !== 3) {
      console.log('❌ Invalid JWT: Should have 3 parts separated by dots');
      return false;
    }
  } else {
    console.log('ℹ️  Token format: API Token (string)');
  }

  console.log('✅ Token format appears valid');
  return true;
}

async function main() {
  const isValidFormat = await validateTokenFormat();
  if (!isValidFormat) {
    console.log('\n❌ Token validation failed. Please check your token.');
    process.exit(1);
  }

  await testAllEndpoints();
}

main().catch(console.error);
