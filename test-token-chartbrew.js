const http = require('http');

// Chartbrew API Token Test
// Usage: node test-token-chartbrew.js [YOUR_CHARTBREW_API_TOKEN]

const API_TOKEN = process.argv[2];

// Chartbrew configuration from your setup
const CHARTBREW_API = 'http://localhost:4019';
const CHARTBREW_FRONTEND = 'http://localhost:4018';
const STRAPI_ORIGIN = 'http://localhost:1337';

if (!API_TOKEN) {
  console.error('🔑 Please provide a Chartbrew API token as a command line argument:');
  console.error('   node test-token-chartbrew.js YOUR_CHARTBREW_API_TOKEN');
  console.error('');
  console.error('📋 To generate a Chartbrew API token:');
  console.error('   1. Go to http://localhost:4018 in your browser');
  console.error('   2. Login to Chartbrew');
  console.error('   3. Navigate to your team settings (click on profile/team name)');
  console.error('   4. Go to "API Keys" or "Integrations" section');
  console.error('   5. Click "Generate new API key" or "Create API Token"');
  console.error('   6. Copy the generated token immediately (you won\'t see it again)');
  process.exit(1);
}

// Test various Chartbrew API endpoints (based on your existing configuration)
const endpoints = [
  { path: '', description: 'API Root' },
  { path: '/user', description: 'User endpoint' },
  { path: '/team', description: 'Team endpoint' },
  { path: '/project', description: 'Projects endpoint' },
  { path: '/chart', description: 'Charts endpoint' },
  { path: '/connection', description: 'Connections endpoint' }
];

function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint.path, CHARTBREW_API);
    
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: 'GET',
      timeout: 15000,
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': STRAPI_ORIGIN,
        'User-Agent': 'Strapi-Chartbrew-Integration/1.0'
      }
    };

    console.log(`\n📡 Testing ${endpoint.description}`);
    console.log(`   URL: ${CHARTBREW_API}${endpoint.path}`);
    console.log(`   Token: ${API_TOKEN.substring(0, 20)}...`);

    const req = http.request(options, (res) => {
      const statusColor = res.statusCode === 200 ? '\x1b[32m' : res.statusCode < 400 ? '\x1b[33m' : '\x1b[31m';
      const resetColor = '\x1b[0m';
      
      console.log(`   Status: ${statusColor}${res.statusCode}${resetColor}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (data) {
          try {
            const jsonData = JSON.parse(data);
            
            if (res.statusCode === 200) {
              console.log(`   ✅ Success!`);
              
              // Handle different response structures
              if (endpoint.path === '/api/v1/teams' && Array.isArray(jsonData)) {
                console.log(`   👥 Found ${jsonData.length} team(s)`);
                jsonData.forEach((team, index) => {
                  console.log(`      ${index + 1}. ${team.name || team.id} (ID: ${team.id})`);
                });
              } else if (endpoint.path === '/api/v1/user') {
                console.log(`   👤 User: ${jsonData.email || jsonData.name || 'Unknown'}`);
                console.log(`   🆔 ID: ${jsonData.id}`);
              } else if (endpoint.path === '/api/v1/projects' && Array.isArray(jsonData)) {
                console.log(`   📊 Found ${jsonData.length} project(s)`);
              } else if (endpoint.path === '/api/v1/charts' && Array.isArray(jsonData)) {
                console.log(`   📈 Found ${jsonData.length} chart(s)`);
              } else if (endpoint.path === '/api/v1/connections' && Array.isArray(jsonData)) {
                console.log(`   🔗 Found ${jsonData.length} connection(s)`);
              } else if (endpoint.path === '' && typeof jsonData === 'string') {
                console.log(`   📄 Response: ${jsonData}`);
              } else {
                const preview = JSON.stringify(jsonData).substring(0, 150);
                console.log(`   📄 Response: ${preview}${preview.length >= 150 ? '...' : ''}`);
              }
            } else {
              console.log(`   ❌ Error Response:`);
              if (jsonData.error) {
                console.log(`      Message: ${jsonData.error.message || jsonData.error}`);
                console.log(`      Status: ${jsonData.error.status || res.statusCode}`);
              } else if (jsonData.message) {
                console.log(`      Message: ${jsonData.message}`);
              } else {
                const preview = JSON.stringify(jsonData).substring(0, 100);
                console.log(`      ${preview}${preview.length >= 100 ? '...' : ''}`);
              }
            }
          } catch (e) {
            console.log(`   📄 Raw Response: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`);
          }
        } else {
          console.log(`   📄 Empty response`);
        }
        resolve({ status: res.statusCode, success: res.statusCode === 200 });
      });
    });

    req.on('error', (error) => {
      console.error(`   ❌ Connection Error: ${error.message}`);
      if (error.code === 'ECONNREFUSED') {
        console.error(`   💡 Hint: Make sure Chartbrew API server is running on port 4019`);
      }
      resolve({ status: 0, success: false, error: error.message });
    });

    req.on('timeout', () => {
      console.error(`   ⏰ Request timed out`);
      req.destroy();
      resolve({ status: 0, success: false, error: 'Timeout' });
    });

    req.end();
  });
}

async function testConnectivity() {
  console.log('\n🔍 Testing Chartbrew API connectivity...');
  
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 4019,
      path: '/',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200 && data.includes('chartBrew')) {
          console.log('✅ Chartbrew API server is running and accessible');
          resolve(true);
        } else {
          console.log(`⚠️  Chartbrew API responded with status ${res.statusCode}`);
          console.log(`   Response: ${data.substring(0, 100)}`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Cannot connect to Chartbrew API: ${error.message}`);
      console.error('💡 Make sure Chartbrew API server is running on http://localhost:4019');
      resolve(false);
    });

    req.on('timeout', () => {
      console.error('⏰ Connection to Chartbrew API timed out');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function testAllEndpoints() {
  console.log(`🚀 Testing Chartbrew API Authentication`);
  console.log(`🕒 ${new Date().toLocaleString()}`);
  console.log(`🔑 Token: ${API_TOKEN.substring(0, 20)}...`);
  console.log(`🌐 Chartbrew API: ${CHARTBREW_API}`);
  console.log(`🌐 Chartbrew Frontend: ${CHARTBREW_FRONTEND}`);
  console.log('='.repeat(60));
  
  // Test connectivity first
  const isConnected = await testConnectivity();
  if (!isConnected) {
    console.log('\n❌ Cannot proceed with authentication tests - Chartbrew API is not accessible');
    console.log('\n🔧 Troubleshooting steps:');
    console.log('   1. Start Chartbrew API server: cd chartbrew/server && npm run start-dev');
    console.log('   2. Verify it\'s running on port 4019');
    console.log('   3. Check Docker containers if using Docker setup');
    return;
  }
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push({ ...endpoint, ...result });
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log('='.repeat(60));
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`✅ Successful: ${successful}/${total}`);
  console.log(`❌ Failed: ${total - successful}/${total}`);
  
  if (successful === total) {
    console.log('\n🎉 All tests passed! Your Chartbrew API token is working correctly.');
    console.log('✨ You can use this token for Strapi-Chartbrew integration.');
  } else if (successful > 0) {
    console.log('\n⚠️  Some tests failed, but authentication appears to be working.');
    console.log('🔍 Check individual endpoint results above for details.');
  } else {
    console.log('\n❌ All tests failed. Check the details above.');
    
    console.log('\n💡 Common issues:');
    console.log('   1. Invalid or expired API token');
    console.log('   2. Token doesn\'t have required permissions');
    console.log('   3. Chartbrew API server configuration issues');
    console.log('   4. Network connectivity problems');
  }
  
  console.log('\n📋 Configuration for Strapi Plugin:');
  console.log(`   Chartbrew API URL: ${CHARTBREW_API}`);
  console.log(`   Chartbrew Frontend URL: ${CHARTBREW_FRONTEND}`);
  console.log(`   API Token: ${API_TOKEN}`);
}

// Additional token validation function
async function validateTokenFormat() {
  console.log('\n🔍 Validating token format...');
  
  if (!API_TOKEN || typeof API_TOKEN !== 'string') {
    console.log('❌ Invalid token: Token must be a non-empty string');
    return false;
  }
  
  if (API_TOKEN.length < 10) {
    console.log('❌ Invalid token: Token appears too short');
    return false;
  }
  
  // Chartbrew tokens are typically long strings
  if (API_TOKEN.length < 50) {
    console.log('⚠️  Warning: Token appears shorter than expected for Chartbrew');
  }
  
  console.log('ℹ️  Token format: Chartbrew API Token');
  console.log('✅ Token format appears valid');
  return true;
}

async function main() {
  const isValidFormat = await validateTokenFormat();
  if (!isValidFormat) {
    console.log('\n❌ Token validation failed. Please check your token.');
    process.exit(1);
  }
  
  await testAllEndpoints();
}

main().catch(console.error);
