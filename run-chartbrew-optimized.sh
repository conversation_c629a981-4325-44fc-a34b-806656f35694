#!/bin/bash

# Optimized Chartbrew Docker Setup for Strapi Integration
# This script runs Chartbrew with host networking and proper Strapi connectivity

echo "🚀 Starting Chartbrew with optimized Docker configuration..."

# Stop and remove existing container if it exists
docker stop tender_vaughan 2>/dev/null || true
docker rm tender_vaughan 2>/dev/null || true

# Create necessary directories
mkdir -p "E:\real estate\chartbrew\data"
mkdir -p "E:\real estate\chartbrew\client-build"

# Run Chartbrew with optimized configuration
docker run -d --name tender_vaughan \
  --net=host \
  -p 4019:4019 -p 4018:4018 \
  -v "E:\real estate\chartbrew\data":/data \
  -v "E:\real estate\chartbrew\client-build":/code/client/build \
  -e CB_ENCRYPTION_KEY=9956f123a9763e4ecbd65ab725ec1a1e91228d753e64c13bbf7ce04e9af7cc67 \
  -e CB_API_HOST=0.0.0.0 \
  -e CB_API_PORT=4019 \
  -e CB_DB_DIALECT=mysql \
  -e CB_DB_HOST=host.docker.internal \
  -e CB_DB_PORT=3306 \
  -e CB_DB_NAME=chartbrew \
  -e CB_DB_USERNAME=root \
  -e CB_DB_PASSWORD=password \
  -e CB_DB_SSL=false \
  -e CB_REDIS_HOST=host.docker.internal \
  -e CB_REDIS_PORT=6379 \
  -e CB_REDIS_PASSWORD=password \
  -e CB_REDIS_DB=0 \
  -e CB_REDIS_CA="" \
  -e VITE_APP_CLIENT_HOST=http://localhost:4018 \
  -e VITE_APP_CLIENT_PORT=4018 \
  -e VITE_APP_API_HOST=http://localhost:4019 \
  -e STRAPI_URL=http://localhost:1337 \
  -e STRAPI_API_URL=http://localhost:1337/api \
  razvanilin/chartbrew

echo "✅ Chartbrew container 'tender_vaughan' started successfully!"
echo "🌐 Frontend: http://localhost:4018"
echo "🔌 API: http://localhost:4019"
echo "📊 Strapi: http://localhost:1337"

# Wait for container to start
echo "⏳ Waiting for Chartbrew to start..."
sleep 10

# Test connectivity
echo "🧪 Testing connectivity..."
curl -s http://localhost:4019 > /dev/null && echo "✅ Chartbrew API is running" || echo "❌ Chartbrew API not responding"
curl -s http://localhost:1337/api/properties > /dev/null && echo "✅ Strapi API is accessible" || echo "❌ Strapi API not accessible"

echo "🎉 Setup complete! You can now:"
echo "   1. Access Chartbrew at http://localhost:4018"
echo "   2. Generate API tokens for Strapi integration"
echo "   3. Test with: node test-token-chartbrew.js YOUR_TOKEN"
