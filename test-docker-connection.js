const http = require('http');

// Test connection from Docker container to Strapi
const STRAPI_HOST = '***********';
const STRAPI_PORT = 1337;

console.log('🐳 Testing Docker → Strapi Connection');
console.log(`📡 Target: http://${STRAPI_HOST}:${STRAPI_PORT}/api/properties`);
console.log('='.repeat(60));

const options = {
  hostname: STRAPI_HOST,
  port: STRAPI_PORT,
  path: '/api/properties',
  method: 'GET',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

const req = http.request(options, (res) => {
  console.log(`✅ Connection successful!`);
  console.log(`📊 Status Code: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const jsonData = JSON.parse(data);
      if (jsonData.data && Array.isArray(jsonData.data)) {
        console.log(`🏠 Found ${jsonData.data.length} properties`);
        console.log(`📈 Pagination: ${JSON.stringify(jsonData.meta?.pagination || {})}`);
      } else {
        console.log(`📄 Response: ${data.substring(0, 200)}...`);
      }
    } catch (e) {
      console.log(`📄 Raw response: ${data.substring(0, 200)}...`);
    }
    
    console.log('\n🎉 Docker networking is working correctly!');
    console.log('✨ Chartbrew should now be able to connect to Strapi');
  });
});

req.on('error', (error) => {
  console.error(`❌ Connection failed: ${error.message}`);
  console.error('💡 Possible solutions:');
  console.error('   1. Check if Strapi is running on the host');
  console.error('   2. Verify firewall settings allow Docker connections');
  console.error('   3. Ensure host IP is correct');
});

req.on('timeout', () => {
  console.error('⏰ Connection timed out');
  req.destroy();
});

req.end();
